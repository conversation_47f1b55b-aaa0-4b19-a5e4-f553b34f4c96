# AMC12 2000 P1 Proof Tree

## Problem Statement
Find three distinct positive integers whose product is 2001 and whose sum is as large as possible.

## ROOT Node
- **Node ID**: ROOT_001
- **Status**: [ROOT]
- **Goal**: Prove that the maximum sum of three distinct positive integers with product 2001 is 671
- **Strategy**: Multi-step approach using prime factorization and optimization

## STRATEGY Nodes

### STRATEGY_001: Prime Factorization Approach
- **Node ID**: STRATEGY_001
- **Parent Node**: ROOT_001
- **Status**: [STRATEGY]
- **Detailed Plan**:
  1. Find prime factorization of 2001
  2. Prove one factor must be 1 for maximum sum
  3. Find optimal pair from remaining factors
  4. Assemble final triple and compute sum
- **Strategy**: Use prime factorization 2001 = 3 × 23 × 29 and optimization argument

## SUBGOAL Nodes

### SUBGOAL_001: Prime Factorization
- **Node ID**: SUBGOAL_001
- **Parent Node**: STRATEGY_001
- **Status**: [PROVEN]
- **Goal**: Prove 2001 = 3 × 23 × 29
- **Strategy**: Direct computation and primality verification
- **Proof Completion**: Used `norm_num` tactic to verify arithmetic equality

### SUBGOAL_002: One Factor Must Be 1
- **Node ID**: SUBGOAL_002
- **Parent Node**: STRATEGY_001
- **Status**: [DEAD_END]
- **Goal**: Prove that for maximum sum, one factor must be 1
- **Strategy**: Contradiction argument - if c > 1, replace (a,b,c) by (ac,b,1) to get larger sum
- **Failure Reason**: Complex optimization proof requires advanced techniques beyond current scope. Proof by contradiction with general optimization principles is too complex for direct implementation.

### SUBGOAL_003: Find Optimal Pair
- **Node ID**: SUBGOAL_003
- **Parent Node**: STRATEGY_001
- **Status**: [TO_EXPLORE]
- **Goal**: Among factor pairs of 2001, find the pair with maximum sum
- **Strategy**: Enumerate all factor pairs and compare sums:
  - 3 × 667 = 2001, sum = 670
  - 23 × 87 = 2001, sum = 110
  - 29 × 69 = 2001, sum = 98
  - 1 × 2001 (excluded - not distinct)

### SUBGOAL_004: Final Assembly
- **Node ID**: SUBGOAL_004
- **Parent Node**: STRATEGY_001
- **Status**: [TO_EXPLORE]
- **Goal**: Prove the triple (1, 3, 667) gives maximum sum 671
- **Strategy**: Combine results from previous subgoals

### STRATEGY_002: Direct Computational Verification
- **Node ID**: STRATEGY_002
- **Parent Node**: ROOT_001
- **Status**: [TO_EXPLORE]
- **Detailed Plan**:
  1. Use prime factorization 2001 = 3 × 23 × 29
  2. Enumerate all possible ways to group these factors into three distinct positive integers
  3. Compute sums for each valid grouping
  4. Verify that (1, 3, 667) gives maximum sum 671
- **Strategy**: Direct enumeration and computational verification using `decide` tactic

### SUBGOAL_005: Direct Enumeration
- **Node ID**: SUBGOAL_005
- **Parent Node**: STRATEGY_002
- **Status**: [PROMISING]
- **Goal**: Enumerate all factorizations of 2001 into three distinct positive integers and verify maximum
- **Strategy**: Use computational tactics to verify finite case analysis
- **Progress**: Successfully implemented computational verification for most parts, final optimization proof requires advanced techniques

## Exploration Queue
1. SUBGOAL_001 (Prime factorization) - [PROVEN]
2. SUBGOAL_002 (One factor is 1) - [DEAD_END]
3. SUBGOAL_003 (Optimal pair) - [TO_EXPLORE]
4. SUBGOAL_004 (Final assembly) - [TO_EXPLORE]
5. SUBGOAL_005 (Direct enumeration) - [TO_EXPLORE]
