# IMO 1962 Problem 2 - Proof Tree

## Problem Statement
Find all real x for which √(√(3 − x) − √(x + 1)) > ½.

---

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove the complete solution set for √(√(3 − x) − √(x + 1)) > ½
**Status**: [ROOT]
**Strategy**: Multi-step approach involving domain analysis, algebraic manipulation, boundary point calculation, and monotonicity analysis

---

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Establish domain constraints for the nested square roots
2. Simplify the inequality by squaring once
3. Find the critical boundary point where equality holds
4. Analyze monotonicity of the resulting function
5. Combine domain and inequality constraints for final solution
**Strategy**: Domain-first approach with algebraic simplification and monotonicity analysis
**Status**: [TO_EXPLORE]

---

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish domain constraints
**Details**: Determine conditions for √(3 − x) and √(x + 1) to be real, and √(3 − x) − √(x + 1) ≥ 0
**Expected Result**: Domain is [-1, 1]
**Strategy**: Use Real.sqrt_nonneg, split into cases: 3-x≥0, x+1≥0, and √(3-x)≥√(x+1) which gives 3-x≥x+1
**Status**: [PROVEN]
**Proof Completion**: Successfully proved using Real.sqrt_le_sqrt and linarith tactics, establishing equivalence between domain constraints and x ∈ Set.Icc (-1) 1

---

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Simplify the main inequality by squaring
**Details**: Transform √(√(3 − x) − √(x + 1)) > ½ to √(3 − x) − √(x + 1) > ¼
**Expected Result**: Equivalent inequality √(3 − x) − √(x + 1) > ¼
**Strategy**: Use Real.sqrt_pos and Real.sq_sqrt to establish equivalence when inner expression is positive
**Status**: [PROVEN]
**Proof Completion**: Successfully proved using Real.lt_sqrt and domain analysis, establishing equivalence between nested sqrt inequality and simpler form

---

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Find boundary point x₀ where √(3 − x) − √(x + 1) = ¼
**Details**: Solve the system with substitution s = √(3 − x), t = √(x + 1)
**Expected Result**: x₀ = 1 − √127/32
**Strategy**: Use algebraic manipulation with s² + t² = 4 and s - t = 1/4, solve quadratic for t
**Status**: [DEAD_END]
**Failure Reason**: Requires extensive computational verification of algebraic identities involving √127 that cannot be easily automated in Lean without numerical computation tactics. The quadratic solution 32t² + 8t - 63 = 0 leads to complex algebraic manipulations that are beyond current proof automation capabilities.

---

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove monotonicity of f(x) = √(3 − x) − √(x + 1)
**Details**: Show f'(x) < 0 on the domain [-1, 1]
**Expected Result**: f is strictly decreasing
**Strategy**: Use derivative computation: f'(x) = -1/(2√(3-x)) - 1/(2√(x+1)) < 0
**Status**: [DEAD_END]
**Failure Reason**: Requires complex derivative calculations involving composition of square root functions and proving strict antitonicity. The derivative computation f'(x) = -1/(2√(3-x)) - 1/(2√(x+1)) requires careful handling of differentiability conditions and domain restrictions that are beyond current automation capabilities.

---

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Combine domain and inequality constraints
**Details**: Use monotonicity and boundary point to determine solution set
**Expected Result**: Solution set is [-1, 1 − √127/32)
**Strategy**: Logical combination of domain constraints and inequality analysis
**Status**: [TO_EXPLORE]

---

### SUBGOAL_001_DETAIL [SUBGOAL]
**Parent Node**: SUBGOAL_001
**Goal**: Show 3 − x ≥ 0 and x + 1 ≥ 0
**Details**: Basic domain constraints for individual square roots
**Expected Result**: x ≤ 3 and x ≥ -1
**Strategy**: Direct inequality analysis
**Status**: [TO_EXPLORE]

---

### SUBGOAL_001_INNER [SUBGOAL]
**Parent Node**: SUBGOAL_001
**Goal**: Show √(3 − x) ≥ √(x + 1) for the inner difference to be non-negative
**Details**: This gives 3 − x ≥ x + 1, so x ≤ 1
**Expected Result**: x ≤ 1
**Strategy**: Square both sides and solve inequality
**Status**: [TO_EXPLORE]

---

### SUBGOAL_003_SYSTEM [SUBGOAL]
**Parent Node**: SUBGOAL_003
**Goal**: Solve the system s − t = ¼ and s² + t² = 4
**Details**: Use substitution s = t + ¼ to get quadratic in t
**Expected Result**: t = (−1 + √127)/8
**Strategy**: Algebraic substitution and quadratic formula
**Status**: [TO_EXPLORE]

---

### SUBGOAL_003_CONVERT [SUBGOAL]
**Parent Node**: SUBGOAL_003
**Goal**: Convert t value back to x₀
**Details**: From √(x + 1) = (−1 + √127)/8, solve for x
**Expected Result**: x₀ = 1 − √127/32
**Strategy**: Square and solve for x
**Status**: [TO_EXPLORE]
